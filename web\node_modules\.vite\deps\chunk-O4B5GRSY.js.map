{"version": 3, "sources": ["../../@radix-ui/react-popper/src/Popper.tsx", "../../@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../@radix-ui/react-arrow/src/Arrow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        isPositioned: true\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ElementRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACEvB,YAAuB;AACvB,mBAA2C;AAC3C,eAA0B;AAE1B,IAAI,QAAQ,OAAO,aAAa,cAAc,+BAAkB;AAIhE,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,EAAE,SAAS,MAAM,EAAE,SAAS,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO,MAAM,UAAU;AACnC,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,WAAW,EAAE,OAAQ,QAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAClC;AAAA,MACF;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AAEA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAY,aAAO,KAAK;AAC9B,QAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,IAAI,CAAC;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS;AAAA,IACrC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAS,UAAU;AACzE,MAAI,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAC5C,wBAAoB,UAAU;AAAA,EAChC;AACA,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,IAAI;AACvD,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,IAAI;AACrD,QAAM,eAAqB,kBAAY,UAAQ;AAC7C,QAAI,SAAS,aAAa,SAAS;AACjC,mBAAa,UAAU;AACvB,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAoB,kBAAY,UAAQ;AAC5C,QAAI,SAAS,YAAY,SAAS;AAChC,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,oBAAoB;AACvC,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,0BAA0B,wBAAwB;AACxD,QAAM,0BAA0B,aAAa,oBAAoB;AACjE,QAAM,cAAc,aAAaA,SAAQ;AACzC,QAAM,SAAe,kBAAY,MAAM;AACrC,QAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;AACjD;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AACA,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW,YAAY;AAAA,IAChC;AACA,oBAAgB,aAAa,SAAS,YAAY,SAAS,MAAM,EAAE,KAAK,CAAAC,UAAQ;AAC9E,YAAM,WAAW;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB;AACA,UAAI,aAAa,WAAW,CAAC,UAAU,QAAQ,SAAS,QAAQ,GAAG;AACjE,gBAAQ,UAAU;AAClB,QAAS,mBAAU,MAAM;AACvB,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,WAAW,UAAU,WAAW,CAAC;AACvD,QAAM,MAAM;AACV,QAAI,SAAS,SAAS,QAAQ,QAAQ,cAAc;AAClD,cAAQ,QAAQ,eAAe;AAC/B,cAAQ,CAAAA,WAAS;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,eAAqB,aAAO,KAAK;AACvC,QAAM,MAAM;AACV,iBAAa,UAAU;AACvB,WAAO,MAAM;AACX,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,MAAM;AACV,QAAI,YAAa,cAAa,UAAU;AACxC,QAAI,WAAY,aAAY,UAAU;AACtC,QAAI,eAAe,YAAY;AAC7B,UAAI,wBAAwB,SAAS;AACnC,eAAO,wBAAwB,QAAQ,aAAa,YAAY,MAAM;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,aAAa,YAAY,QAAQ,yBAAyB,uBAAuB,CAAC;AACtF,QAAM,OAAa,cAAQ,OAAO;AAAA,IAChC,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI,CAAC,cAAc,WAAW,CAAC;AAC/B,QAAM,WAAiB,cAAQ,OAAO;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,iBAAuB,cAAQ,MAAM;AACzC,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,UAAU;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,QAAI,WAAW;AACb,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,IAAI,SAAS,IAAI;AAAA,QAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,UACtC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3D,SAAa,cAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,QAAQ,MAAM,UAAU,cAAc,CAAC;AACpD;AAQA,IAAM,UAAU,aAAW;AACzB,WAAS,MAAM,OAAO;AACpB,WAAO,CAAC,EAAE,eAAe,KAAK,OAAO,SAAS;AAAA,EAChD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACrD,UAAI,WAAW,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAO,MAAQ;AAAA,YACb,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,GAAG,KAAK;AAAA,QACb;AACA,eAAO,CAAC;AAAA,MACV;AACA,UAAI,SAAS;AACX,eAAO,MAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC,EAAE,GAAG,KAAK;AAAA,MACb;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AASA,IAAMC,UAAS,CAAC,SAAS,UAAU;AAAA,EACjC,GAAG,OAAS,OAAO;AAAA,EACnB,SAAS,CAAC,SAAS,IAAI;AACzB;AAOA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAG,MAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;AAIA,IAAMC,cAAa,CAAC,SAAS,UAAU;AAAA,EACrC,GAAG,WAAa,OAAO;AAAA,EACvB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAG,KAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAG,KAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAG,KAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAG,QAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;;;ACvWA,IAAAC,SAAuB;AAyBW,yBAAA;AAlBlC,IAAM,OAAO;AAMb,IAAM,QAAc,kBAAqC,CAAC,OAAO,iBAAiB;AAChF,QAAM,EAAE,UAAU,QAAQ,IAAI,SAAS,GAAG,GAAG,WAAW,IAAI;AAC5D,aACE;IAAC,UAAU;IAAV;MACE,GAAG;MACJ,KAAK;MACL;MACA;MACA,SAAQ;MACR,qBAAoB;MAGnB,UAAA,MAAM,UAAU,eAAW,wBAAC,WAAA,EAAQ,QAAO,iBAAA,CAAiB;IAAA;EAC/D;AAEJ,CAAC;AAED,MAAM,cAAc;AAIpB,IAAM,OAAO;;;AFkBT,IAAAC,sBAAA;AAlBJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAM/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAK9F,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM,EAAE,eAAe,SAAS,IAAI;AACpC,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAA4B,IAAI;AAClE,aACE,yBAAC,gBAAA,EAAe,OAAO,eAAe,QAAgB,gBAAgB,WACnE,SAAA,CACH;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,cAAc;AAQpB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,eAAe,YAAY,GAAG,YAAY,IAAI;AACtD,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,UAAM,MAAY,cAA4B,IAAI;AAClD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAEhD,IAAA,iBAAU,MAAM;AAIpB,cAAQ,gBAAe,yCAAY,YAAW,IAAI,OAAO;IAC3D,CAAC;AAED,WAAO,aAAa,WAAO,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAUrB,IAAM,CAAC,uBAAuB,iBAAiB,IAC7C,oBAA+C,YAAY;AAoB7D,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;;AACxD,UAAM;MACJ;MACA,OAAO;MACP,aAAa;MACb,QAAQ;MACR,cAAc;MACd,eAAe;MACf,kBAAkB;MAClB,oBAAoB,CAAC;MACrB,kBAAkB,uBAAuB;MACzC,SAAS;MACT,mBAAmB;MACnB,yBAAyB;MACzB;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAE5D,UAAM,CAAC,SAAS,UAAU,IAAU,gBAAgC,IAAI;AACxE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAE7E,UAAM,CAACC,QAAO,QAAQ,IAAU,gBAAiC,IAAI;AACrE,UAAM,YAAY,QAAQA,MAAK;AAC/B,UAAM,cAAa,uCAAW,UAAS;AACvC,UAAM,eAAc,uCAAW,WAAU;AAEzC,UAAM,mBAAoB,QAAQ,UAAU,WAAW,MAAM,QAAQ;AAErE,UAAM,mBACJ,OAAO,yBAAyB,WAC5B,uBACA,EAAE,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,GAAG,qBAAqB;AAEtE,UAAM,WAAW,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB,CAAC,iBAAiB;AAC1F,UAAM,wBAAwB,SAAS,SAAS;AAEhD,UAAM,wBAAwB;MAC5B,SAAS;MACT,UAAU,SAAS,OAAO,SAAS;;MAEnC,aAAa;IACf;AAEA,UAAM,EAAE,MAAM,gBAAgB,WAAW,cAAc,eAAe,IAAI,YAAY;;MAEpF,UAAU;MACV,WAAW;MACX,sBAAsB,IAAI,SAAS;AACjC,cAAM,UAAU,WAAW,GAAG,MAAM;UAClC,gBAAgB,2BAA2B;QAC7C,CAAC;AACD,eAAO;MACT;MACA,UAAU;QACR,WAAW,QAAQ;MACrB;MACA,YAAY;QACVC,QAAO,EAAE,UAAU,aAAa,aAAa,eAAe,YAAY,CAAC;QACzE,mBACEC,OAAM;UACJ,UAAU;UACV,WAAW;UACX,SAAS,WAAW,YAAYC,YAAW,IAAI;UAC/C,GAAG;QACL,CAAC;QACH,mBAAmBC,MAAK,EAAE,GAAG,sBAAsB,CAAC;QACpDC,MAAK;UACH,GAAG;UACH,OAAO,CAAC,EAAE,UAAU,OAAO,gBAAgB,gBAAgB,MAAM;AAC/D,kBAAM,EAAE,OAAO,aAAa,QAAQ,aAAa,IAAI,MAAM;AAC3D,kBAAM,eAAe,SAAS,SAAS;AACvC,yBAAa,YAAY,kCAAkC,GAAG,cAAc,IAAI;AAChF,yBAAa,YAAY,mCAAmC,GAAG,eAAe,IAAI;AAClF,yBAAa,YAAY,+BAA+B,GAAG,WAAW,IAAI;AAC1E,yBAAa,YAAY,gCAAgC,GAAG,YAAY,IAAI;UAC9E;QACF,CAAC;QACDL,UAASA,OAAgB,EAAE,SAASA,QAAO,SAAS,aAAa,CAAC;QAClE,gBAAgB,EAAE,YAAY,YAAY,CAAC;QAC3C,oBAAoBM,MAAK,EAAE,UAAU,mBAAmB,GAAG,sBAAsB,CAAC;MACpF;IACF,CAAC;AAED,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AAExE,UAAM,eAAe,eAAe,QAAQ;AAC5C,qBAAgB,MAAM;AACpB,UAAI,cAAc;AAChB;MACF;IACF,GAAG,CAAC,cAAc,YAAY,CAAC;AAE/B,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AAEjE,UAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAiB;AACjE,qBAAgB,MAAM;AACpB,UAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;IACvE,GAAG,CAAC,OAAO,CAAC;AAEZ,eACE;MAAC;MAAA;QACC,KAAK,KAAK;QACV,qCAAkC;QAClC,OAAO;UACL,GAAG;UACH,WAAW,eAAe,eAAe,YAAY;;UACrD,UAAU;UACV,QAAQ;UACR,CAAC,iCAAwC,GAAG;aAC1C,oBAAe,oBAAf,mBAAgC;aAChC,oBAAe,oBAAf,mBAAgC;UAClC,EAAE,KAAK,GAAG;;;;UAKV,KAAI,oBAAe,SAAf,mBAAqB,oBAAmB;YAC1C,YAAY;YACZ,eAAe;UACjB;QACF;QAIA,KAAK,MAAM;QAEX,cAAA;UAAC;UAAA;YACC,OAAO;YACP;YACA,eAAe;YACf;YACA;YACA,iBAAiB;YAEjB,cAAA;cAAC,UAAU;cAAV;gBACC,aAAW;gBACX,cAAY;gBACX,GAAG;gBACJ,KAAK;gBACL,OAAO;kBACL,GAAG,aAAa;;;kBAGhB,WAAW,CAAC,eAAe,SAAS;gBACtC;cAAA;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAEnB,IAAM,gBAAoC;EACxC,KAAK;EACL,OAAO;EACP,QAAQ;EACR,MAAM;AACR;AAMA,IAAM,cAAoB,kBAAiD,SAASC,aAClF,OACA,cACA;AACA,QAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,QAAM,iBAAiB,kBAAkB,YAAY,aAAa;AAClE,QAAM,WAAW,cAAc,eAAe,UAAU;AAExD;;;;QAIE;MAAC;MAAA;QACC,KAAK,eAAe;QACpB,OAAO;UACL,UAAU;UACV,MAAM,eAAe;UACrB,KAAK,eAAe;UACpB,CAAC,QAAQ,GAAG;UACZ,iBAAiB;YACf,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,WAAW;YACT,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,YAAY,eAAe,kBAAkB,WAAW;QAC1D;QAEA,cAAA;UAAgB;UAAf;YACE,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,WAAW;;cAEd,SAAS;YACX;UAAA;QACF;MAAA;IACF;;AAEJ,CAAC;AAED,YAAY,cAAc;AAI1B,SAAS,UAAa,OAA6B;AACjD,SAAO,UAAU;AACnB;AAEA,IAAM,kBAAkB,CAAC,aAAsE;EAC7F,MAAM;EACN;EACA,GAAG,MAAM;;AACP,UAAM,EAAE,WAAW,OAAO,eAAe,IAAI;AAE7C,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AACjE,UAAM,gBAAgB;AACtB,UAAM,aAAa,gBAAgB,IAAI,QAAQ;AAC/C,UAAM,cAAc,gBAAgB,IAAI,QAAQ;AAEhD,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AACxE,UAAM,eAAe,EAAE,OAAO,MAAM,QAAQ,OAAO,KAAK,OAAO,EAAE,WAAW;AAE5E,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,aAAa;AACnE,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,cAAc;AAEpE,QAAI,IAAI;AACR,QAAI,IAAI;AAER,QAAI,eAAe,UAAU;AAC3B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,CAAC,WAAW;IACrB,WAAW,eAAe,OAAO;AAC/B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,MAAM,SAAS,SAAS,WAAW;IAC5C,WAAW,eAAe,SAAS;AACjC,UAAI,GAAG,CAAC,WAAW;AACnB,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD,WAAW,eAAe,QAAQ;AAChC,UAAI,GAAG,MAAM,SAAS,QAAQ,WAAW;AACzC,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD;AACA,WAAO,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;EAC1B;AACF;AAEA,SAAS,6BAA6B,WAAsB;AAC1D,QAAM,CAAC,MAAM,QAAQ,QAAQ,IAAI,UAAU,MAAM,GAAG;AACpD,SAAO,CAAC,MAAc,KAAc;AACtC;AAEA,IAAMC,QAAO;AACb,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAMC,SAAQ;", "names": ["React", "platform", "data", "offset", "shift", "limitShift", "flip", "size", "hide", "arrow", "React", "import_jsx_runtime", "arrow", "offset", "shift", "limitShift", "flip", "size", "hide", "PopperArrow", "Root", "Arrow"]}